package services

import (
	"context"
	"fmt"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type ServiceService struct {
	serviceRepo *repositories.ServiceRepository
	logger      *logrus.Logger
}

func NewServiceService(serviceRepo *repositories.ServiceRepository, logger *logrus.Logger) *ServiceService {
	return &ServiceService{
		serviceRepo: serviceRepo,
		logger:      logger,
	}
}

// Service methods
func (s *ServiceService) GetServices(ctx context.Context, merchantID uuid.UUID, filters types.ServiceFilters) (*types.ServicesResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	services, total, err := s.serviceRepo.GetServices(ctx, merchantID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get services")
		return nil, fmt.Errorf("failed to get services: %w", err)
	}

	// Convert to response format
	serviceResponses := make([]types.ServiceResponse, len(services))
	for i, service := range services {
		serviceResponses[i] = s.convertServiceToResponse(service)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.ServicesResponse{
		Data:       serviceResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *ServiceService) GetServiceByID(ctx context.Context, serviceID uuid.UUID) (*types.ServiceResponse, error) {
	service, err := s.serviceRepo.GetServiceByID(ctx, serviceID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get service by ID")
		return nil, fmt.Errorf("failed to get service: %w", err)
	}

	response := s.convertServiceToResponse(*service)
	return &response, nil
}

func (s *ServiceService) CreateService(ctx context.Context, merchantID uuid.UUID, req types.CreateServiceRequest) (*types.ServiceResponse, error) {
	service := &models.Service{
		MerchantID:      merchantID,
		BranchID:        req.BranchID,
		Name:            req.Name,
		Description:     req.Description,
		Category:        req.Category,
		Price:           req.Price,
		Duration:        req.Duration,
		MaxCapacity:     req.MaxCapacity,
		RequiresStaff:   req.RequiresStaff,
		PreparationTime: req.PreparationTime,
		CleanupTime:     req.CleanupTime,
		ImageURL:        req.ImageURL,
		IsActive:        true,
	}

	if err := s.serviceRepo.CreateService(ctx, service); err != nil {
		s.logger.WithError(err).Error("Failed to create service")
		return nil, fmt.Errorf("failed to create service: %w", err)
	}

	response := s.convertServiceToResponse(*service)
	return &response, nil
}

func (s *ServiceService) UpdateService(ctx context.Context, serviceID uuid.UUID, req types.UpdateServiceRequest) (*types.ServiceResponse, error) {
	updates := make(map[string]interface{})

	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Category != nil {
		updates["category"] = *req.Category
	}
	if req.Price != nil {
		updates["price"] = *req.Price
	}
	if req.Duration != nil {
		updates["duration"] = *req.Duration
	}
	if req.MaxCapacity != nil {
		updates["max_capacity"] = *req.MaxCapacity
	}
	if req.RequiresStaff != nil {
		updates["requires_staff"] = *req.RequiresStaff
	}
	if req.PreparationTime != nil {
		updates["preparation_time"] = *req.PreparationTime
	}
	if req.CleanupTime != nil {
		updates["cleanup_time"] = *req.CleanupTime
	}
	if req.ImageURL != nil {
		updates["image_url"] = *req.ImageURL
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	service, err := s.serviceRepo.UpdateService(ctx, serviceID, updates)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update service")
		return nil, fmt.Errorf("failed to update service: %w", err)
	}

	response := s.convertServiceToResponse(*service)
	return &response, nil
}

func (s *ServiceService) DeleteService(ctx context.Context, serviceID uuid.UUID) error {
	if err := s.serviceRepo.DeleteService(ctx, serviceID); err != nil {
		s.logger.WithError(err).Error("Failed to delete service")
		return fmt.Errorf("failed to delete service: %w", err)
	}

	return nil
}

// Appointment methods
func (s *ServiceService) GetAppointments(ctx context.Context, merchantID uuid.UUID, filters types.AppointmentFilters) (*types.AppointmentsResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	appointments, total, err := s.serviceRepo.GetAppointments(ctx, merchantID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get appointments")
		return nil, fmt.Errorf("failed to get appointments: %w", err)
	}

	// Convert to response format
	appointmentResponses := make([]types.AppointmentResponse, len(appointments))
	for i, appointment := range appointments {
		appointmentResponses[i] = s.convertAppointmentToResponse(appointment)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.AppointmentsResponse{
		Data:       appointmentResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *ServiceService) GetAppointmentByID(ctx context.Context, appointmentID uuid.UUID) (*types.AppointmentResponse, error) {
	appointment, err := s.serviceRepo.GetAppointmentByID(ctx, appointmentID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get appointment by ID")
		return nil, fmt.Errorf("failed to get appointment: %w", err)
	}

	response := s.convertAppointmentToResponse(*appointment)
	return &response, nil
}

func (s *ServiceService) CreateAppointment(ctx context.Context, req types.CreateAppointmentRequest) (*types.AppointmentResponse, error) {
	// Calculate end time based on service duration
	service, err := s.serviceRepo.GetServiceByID(ctx, req.ServiceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get service: %w", err)
	}

	endTime := req.StartTime.Add(time.Duration(service.Duration) * time.Minute)

	appointment := &models.Appointment{
		ServiceID:       req.ServiceID,
		StaffID:         req.StaffID,
		CustomerName:    req.CustomerName,
		CustomerEmail:   req.CustomerEmail,
		CustomerPhone:   req.CustomerPhone,
		AppointmentDate: req.AppointmentDate,
		StartTime:       req.StartTime,
		EndTime:         endTime,
		Duration:        service.Duration,
		PartySize:       req.PartySize,
		Status:          "pending",
		Notes:           req.Notes,
		SpecialRequests: req.SpecialRequests,
		Source:          req.Source,
		TotalPrice:      service.Price * float64(req.PartySize),
	}

	if err := s.serviceRepo.CreateAppointment(ctx, appointment); err != nil {
		s.logger.WithError(err).Error("Failed to create appointment")
		return nil, fmt.Errorf("failed to create appointment: %w", err)
	}

	// Reload with relationships
	appointment, err = s.serviceRepo.GetAppointmentByID(ctx, appointment.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to reload appointment: %w", err)
	}

	response := s.convertAppointmentToResponse(*appointment)
	return &response, nil
}

func (s *ServiceService) UpdateAppointment(ctx context.Context, appointmentID uuid.UUID, req types.UpdateAppointmentRequest) (*types.AppointmentResponse, error) {
	updates := make(map[string]interface{})

	if req.StaffID != nil {
		updates["staff_id"] = *req.StaffID
	}
	if req.CustomerName != nil {
		updates["customer_name"] = *req.CustomerName
	}
	if req.CustomerEmail != nil {
		updates["customer_email"] = *req.CustomerEmail
	}
	if req.CustomerPhone != nil {
		updates["customer_phone"] = *req.CustomerPhone
	}
	if req.AppointmentDate != nil {
		updates["appointment_date"] = *req.AppointmentDate
	}
	if req.StartTime != nil {
		updates["start_time"] = *req.StartTime
		// Recalculate end time if start time changes
		appointment, err := s.serviceRepo.GetAppointmentByID(ctx, appointmentID)
		if err == nil {
			endTime := req.StartTime.Add(time.Duration(appointment.Duration) * time.Minute)
			updates["end_time"] = endTime
		}
	}
	if req.PartySize != nil {
		updates["party_size"] = *req.PartySize
	}
	if req.Status != nil {
		updates["status"] = *req.Status
		if *req.Status == "confirmed" {
			now := time.Now()
			updates["confirmed_at"] = &now
		} else if *req.Status == "completed" {
			now := time.Now()
			updates["completed_at"] = &now
		}
	}
	if req.Notes != nil {
		updates["notes"] = *req.Notes
	}
	if req.SpecialRequests != nil {
		updates["special_requests"] = *req.SpecialRequests
	}

	appointment, err := s.serviceRepo.UpdateAppointment(ctx, appointmentID, updates)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update appointment")
		return nil, fmt.Errorf("failed to update appointment: %w", err)
	}

	response := s.convertAppointmentToResponse(*appointment)
	return &response, nil
}

func (s *ServiceService) CancelAppointment(ctx context.Context, appointmentID uuid.UUID, reason string) (*types.AppointmentResponse, error) {
	appointment, err := s.serviceRepo.CancelAppointment(ctx, appointmentID, reason)
	if err != nil {
		s.logger.WithError(err).Error("Failed to cancel appointment")
		return nil, fmt.Errorf("failed to cancel appointment: %w", err)
	}

	response := s.convertAppointmentToResponse(*appointment)
	return &response, nil
}

// Helper methods
func (s *ServiceService) convertServiceToResponse(service models.Service) types.ServiceResponse {
	return types.ServiceResponse{
		ID:              service.ID,
		MerchantID:      service.MerchantID,
		BranchID:        service.BranchID,
		Name:            service.Name,
		Description:     service.Description,
		Category:        service.Category,
		Price:           service.Price,
		Duration:        service.Duration,
		MaxCapacity:     service.MaxCapacity,
		RequiresStaff:   service.RequiresStaff,
		PreparationTime: service.PreparationTime,
		CleanupTime:     service.CleanupTime,
		ImageURL:        service.ImageURL,
		IsActive:        service.IsActive,
		CreatedAt:       service.CreatedAt,
		UpdatedAt:       service.UpdatedAt,
	}
}

func (s *ServiceService) convertAppointmentToResponse(appointment models.Appointment) types.AppointmentResponse {
	response := types.AppointmentResponse{
		ID:              appointment.ID,
		ServiceID:       appointment.ServiceID,
		StaffID:         appointment.StaffID,
		CustomerName:    appointment.CustomerName,
		CustomerEmail:   appointment.CustomerEmail,
		CustomerPhone:   appointment.CustomerPhone,
		AppointmentDate: appointment.AppointmentDate,
		StartTime:       appointment.StartTime,
		EndTime:         appointment.EndTime,
		Duration:        appointment.Duration,
		PartySize:       appointment.PartySize,
		Status:          appointment.Status,
		Notes:           appointment.Notes,
		SpecialRequests: appointment.SpecialRequests,
		Source:          appointment.Source,
		TotalPrice:      appointment.TotalPrice,
		DepositPaid:     appointment.DepositPaid,
		CreatedAt:       appointment.CreatedAt,
		UpdatedAt:       appointment.UpdatedAt,
	}

	// Add related data if available
	if appointment.Service.ID != uuid.Nil {
		serviceResponse := s.convertServiceToResponse(appointment.Service)
		response.Service = &serviceResponse
	}

	if appointment.Staff != nil && appointment.Staff.ID != uuid.Nil {
		staffResponse := s.convertStaffToResponse(*appointment.Staff)
		response.Staff = &staffResponse
	}

	return response
}

func (s *ServiceService) convertStaffToResponse(staff models.Staff) types.StaffResponse {
	return types.StaffResponse{
		ID:             staff.ID,
		MerchantID:     staff.MerchantID,
		BranchID:       staff.BranchID,
		FirstName:      staff.FirstName,
		LastName:       staff.LastName,
		Slug:           staff.GetSlug(),
		Email:          staff.Email,
		Phone:          staff.Phone,
		Position:       staff.Position,
		Department:     staff.Department,
		EmployeeID:     staff.EmployeeID,
		HireDate:       staff.HireDate,
		Status:         staff.Status,
		AvatarURL:      staff.AvatarURL,
		Bio:            staff.Bio,
		Specialties:    staff.Specialties,
		Languages:      staff.Languages,
		Certifications: staff.Certifications,
		IsActive:       staff.IsActive,
		CreatedAt:      staff.CreatedAt,
		UpdatedAt:      staff.UpdatedAt,
	}
}

// Staff methods
func (s *ServiceService) GetStaff(ctx context.Context, merchantID uuid.UUID, filters types.StaffFilters) (*types.StaffListResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	// Set default sorting
	if filters.SortBy == "" {
		filters.SortBy = "first_name"
	}
	if filters.SortOrder == "" {
		filters.SortOrder = "asc"
	}

	staff, total, err := s.serviceRepo.GetStaff(ctx, merchantID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get staff")
		return nil, fmt.Errorf("failed to get staff: %w", err)
	}

	// Convert to response format
	staffResponses := make([]types.StaffResponse, len(staff))
	for i, member := range staff {
		staffResponses[i] = s.convertStaffToResponse(member)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.StaffListResponse{
		Data:       staffResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *ServiceService) GetStaffByID(ctx context.Context, staffID uuid.UUID) (*types.StaffResponse, error) {
	staff, err := s.serviceRepo.GetStaffByID(ctx, staffID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get staff by ID")
		return nil, fmt.Errorf("failed to get staff: %w", err)
	}

	response := s.convertStaffToResponse(*staff)
	return &response, nil
}

func (s *ServiceService) CreateStaff(ctx context.Context, merchantID uuid.UUID, req types.CreateStaffRequest) (*types.StaffResponse, error) {
	staff := &models.Staff{
		MerchantID:     merchantID,
		BranchID:       req.BranchID,
		UserID:         req.UserID,
		FirstName:      req.FirstName,
		LastName:       req.LastName,
		Email:          req.Email,
		Phone:          req.Phone,
		Position:       req.Position,
		Department:     req.Department,
		EmployeeID:     req.EmployeeID,
		HireDate:       req.HireDate,
		Salary:         req.Salary,
		HourlyRate:     req.HourlyRate,
		Status:         "active",
		AvatarURL:      req.AvatarURL,
		Bio:            req.Bio,
		Specialties:    req.Specialties,
		Languages:      req.Languages,
		Certifications: req.Certifications,
		IsActive:       true,
	}

	if err := s.serviceRepo.CreateStaff(ctx, staff); err != nil {
		s.logger.WithError(err).Error("Failed to create staff")
		return nil, fmt.Errorf("failed to create staff: %w", err)
	}

	response := s.convertStaffToResponse(*staff)
	return &response, nil
}

func (s *ServiceService) UpdateStaff(ctx context.Context, staffID uuid.UUID, req types.UpdateStaffRequest) (*types.StaffResponse, error) {
	updates := make(map[string]interface{})

	if req.FirstName != nil {
		updates["first_name"] = *req.FirstName
	}
	if req.LastName != nil {
		updates["last_name"] = *req.LastName
	}
	if req.Email != nil {
		updates["email"] = *req.Email
	}
	if req.Phone != nil {
		updates["phone"] = *req.Phone
	}
	if req.Position != nil {
		updates["position"] = *req.Position
	}
	if req.Department != nil {
		updates["department"] = *req.Department
	}
	if req.EmployeeID != nil {
		updates["employee_id"] = *req.EmployeeID
	}
	if req.HireDate != nil {
		updates["hire_date"] = *req.HireDate
	}
	if req.Salary != nil {
		updates["salary"] = *req.Salary
	}
	if req.HourlyRate != nil {
		updates["hourly_rate"] = *req.HourlyRate
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	if req.AvatarURL != nil {
		updates["avatar_url"] = *req.AvatarURL
	}
	if req.Bio != nil {
		updates["bio"] = *req.Bio
	}
	if req.Specialties != nil {
		updates["specialties"] = *req.Specialties
	}
	if req.Languages != nil {
		updates["languages"] = *req.Languages
	}
	if req.Certifications != nil {
		updates["certifications"] = *req.Certifications
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	staff, err := s.serviceRepo.UpdateStaff(ctx, staffID, updates)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update staff")
		return nil, fmt.Errorf("failed to update staff: %w", err)
	}

	response := s.convertStaffToResponse(*staff)
	return &response, nil
}

func (s *ServiceService) DeleteStaff(ctx context.Context, staffID uuid.UUID) error {
	if err := s.serviceRepo.DeleteStaff(ctx, staffID); err != nil {
		s.logger.WithError(err).Error("Failed to delete staff")
		return fmt.Errorf("failed to delete staff: %w", err)
	}

	return nil
}

// Availability methods
func (s *ServiceService) GetAvailability(ctx context.Context, req types.AvailabilityRequest) (*types.ServiceAvailabilityResponse, error) {
	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		return nil, fmt.Errorf("invalid date format: %w", err)
	}

	availability, err := s.serviceRepo.GetServiceAvailability(ctx, req.ServiceID, date, &req.StaffID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get availability")
		return nil, fmt.Errorf("failed to get availability: %w", err)
	}

	// Convert to response format (simplified)
	response := &types.ServiceAvailabilityResponse{
		Date:      req.Date,
		Available: len(availability) > 0,
		TimeSlots: []types.ServiceTimeSlot{},
	}

	return response, nil
}

func (s *ServiceService) GetAvailableTimeSlots(ctx context.Context, req types.TimeSlotRequest) ([]string, error) {
	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		return nil, fmt.Errorf("invalid date format: %w", err)
	}

	var staffID *uuid.UUID
	if req.StaffID != uuid.Nil {
		staffID = &req.StaffID
	}

	timeSlots, err := s.serviceRepo.GetAvailableTimeSlots(ctx, req.ServiceID, date, staffID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get available time slots")
		return nil, fmt.Errorf("failed to get available time slots: %w", err)
	}

	return timeSlots, nil
}
