package handlers

import (
	"net/http"
	"strconv"
	"time"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// OrderHandler handles order-related HTTP requests
type OrderHandler struct {
	orderService *services.OrderService
	logger       *logrus.Logger
}

func NewOrderHandler(orderService *services.OrderService, logger *logrus.Logger) *OrderHandler {
	return &OrderHandler{orderService: orderService, logger: logger}
}

// Order handlers

// GetOrders godoc
// @Summary Get all orders for a branch
// @Description Get all orders for a specific branch with filtering and pagination
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param status query string false "Filter by status"
// @Param order_type query string false "Filter by order type"
// @Param table_id query string false "Filter by table ID"
// @Param customer_name query string false "Filter by customer name"
// @Param date_from query string false "Filter from date (RFC3339)"
// @Param date_to query string false "Filter to date (RFC3339)"
// @Param search query string false "Search term"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} types.OrdersResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/orders [get]
func (h *OrderHandler) GetOrders(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.OrderFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	orders, err := h.orderService.GetOrders(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.Error("Failed to get orders: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get orders"})
		return
	}

	c.JSON(http.StatusOK, orders)
}

// GetOrder godoc
// @Summary Get a specific order
// @Description Get a specific order by ID
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param orderId path string true "Order ID"
// @Success 200 {object} types.OrderResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/orders/{orderId} [get]
func (h *OrderHandler) GetOrder(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	orderID, err := uuid.Parse(c.Param("orderId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	order, err := h.orderService.GetOrderByID(c.Request.Context(), branchID, orderID)
	if err != nil {
		h.logger.Error("Failed to get order: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Order not found"})
		return
	}

	c.JSON(http.StatusOK, order)
}

// CreateOrder godoc
// @Summary Create a new order
// @Description Create a new order for a branch
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param order body types.CreateOrderRequest true "Order data"
// @Success 201 {object} types.OrderResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/orders [post]
func (h *OrderHandler) CreateOrder(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	order, err := h.orderService.CreateOrder(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.Error("Failed to create order: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create order"})
		return
	}

	c.JSON(http.StatusCreated, order)
}

// UpdateOrder godoc
// @Summary Update an order
// @Description Update a specific order by ID
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param orderId path string true "Order ID"
// @Param order body types.UpdateOrderRequest true "Order update data"
// @Success 200 {object} types.OrderResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/orders/{orderId} [put]
func (h *OrderHandler) UpdateOrder(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	orderID, err := uuid.Parse(c.Param("orderId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	var req types.UpdateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	order, err := h.orderService.UpdateOrder(c.Request.Context(), branchID, orderID, req)
	if err != nil {
		h.logger.Error("Failed to update order: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update order"})
		return
	}

	c.JSON(http.StatusOK, order)
}

// UpdateOrderStatus godoc
// @Summary Update order status
// @Description Update the status of a specific order
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param orderId path string true "Order ID"
// @Param status body types.UpdateOrderStatusRequest true "Status data"
// @Success 200 {object} types.OrderResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/orders/{orderId}/status [patch]
func (h *OrderHandler) UpdateOrderStatus(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	orderID, err := uuid.Parse(c.Param("orderId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	var req types.UpdateOrderStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	order, err := h.orderService.UpdateOrderStatus(c.Request.Context(), branchID, orderID, req)
	if err != nil {
		h.logger.Error("Failed to update order status: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update order status"})
		return
	}

	c.JSON(http.StatusOK, order)
}

// DeleteOrder godoc
// @Summary Delete an order
// @Description Delete a specific order by ID
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param orderId path string true "Order ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/orders/{orderId} [delete]
func (h *OrderHandler) DeleteOrder(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	orderID, err := uuid.Parse(c.Param("orderId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	if err := h.orderService.DeleteOrder(c.Request.Context(), branchID, orderID); err != nil {
		h.logger.Error("Failed to delete order: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete order"})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetActiveOrders godoc
// @Summary Get active orders
// @Description Get all active orders for a branch
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Success 200 {array} types.OrderResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/orders/active [get]
func (h *OrderHandler) GetActiveOrders(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	orders, err := h.orderService.GetActiveOrders(c.Request.Context(), branchID)
	if err != nil {
		h.logger.Error("Failed to get active orders: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get active orders"})
		return
	}

	c.JSON(http.StatusOK, orders)
}

// Analytics and Dashboard handlers

// GetOrderStats godoc
// @Summary Get order statistics
// @Description Get order statistics for a branch within a date range
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param date_from query string false "Start date (RFC3339)"
// @Param date_to query string false "End date (RFC3339)"
// @Success 200 {object} types.OrderStatsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/reports/orders [get]
func (h *OrderHandler) GetOrderStats(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Parse date range
	dateFromStr := c.Query("date_from")
	dateToStr := c.Query("date_to")

	var dateFrom, dateTo time.Time
	if dateFromStr != "" {
		dateFrom, err = time.Parse(time.RFC3339, dateFromStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid date_from format"})
			return
		}
	} else {
		dateFrom = time.Now().AddDate(0, -1, 0) // Default to 1 month ago
	}

	if dateToStr != "" {
		dateTo, err = time.Parse(time.RFC3339, dateToStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid date_to format"})
			return
		}
	} else {
		dateTo = time.Now() // Default to now
	}

	stats, err := h.orderService.GetOrderStats(c.Request.Context(), branchID, dateFrom, dateTo)
	if err != nil {
		h.logger.Error("Failed to get order stats: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get order stats"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetDashboardStats godoc
// @Summary Get dashboard statistics
// @Description Get dashboard statistics for a branch
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Success 200 {object} types.DashboardStatsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/dashboard/stats [get]
func (h *OrderHandler) GetDashboardStats(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	stats, err := h.orderService.GetDashboardStats(c.Request.Context(), branchID)
	if err != nil {
		h.logger.Error("Failed to get dashboard stats: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get dashboard stats"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetRecentActivity godoc
// @Summary Get recent order activity
// @Description Get recent order activity for a branch
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param limit query int false "Number of orders to return"
// @Success 200 {object} types.RecentActivityResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/dashboard/activity [get]
func (h *OrderHandler) GetRecentActivity(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	limit := 10 // default
	if limitStr := c.Query("limit"); limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	activity, err := h.orderService.GetRecentActivity(c.Request.Context(), branchID, limit)
	if err != nil {
		h.logger.Error("Failed to get recent activity: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recent activity"})
		return
	}

	c.JSON(http.StatusOK, activity)
}

// GetDashboardStatsBySlug godoc
// @Summary Get dashboard statistics by shop slug
// @Description Get dashboard statistics for a shop using slug and branch slug (query parameter)
// @Tags orders
// @Accept json
// @Produce json
// @Param slug path string true "Shop Slug"
// @Param branchSlug query string true "Branch Slug"
// @Success 200 {object} types.FrontendDashboardStatsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/slug/{slug}/dashboard/stats [get]
func (h *OrderHandler) GetDashboardStatsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Query("branchSlug")

	if shopSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug is required"})
		return
	}

	if branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Branch slug is required"})
		return
	}

	stats, err := h.orderService.GetDashboardStatsBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.Error("Failed to get dashboard stats by slug: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get dashboard stats"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetRealTimeMetricsBySlug godoc
// @Summary Get real-time metrics by shop slug
// @Description Get real-time metrics for a shop using slug and branch slug (query parameter)
// @Tags orders
// @Accept json
// @Produce json
// @Param slug path string true "Shop Slug"
// @Param branchSlug query string true "Branch Slug"
// @Success 200 {object} types.RealTimeMetricsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/slug/{slug}/metrics/realtime [get]
func (h *OrderHandler) GetRealTimeMetricsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Query("branchSlug")

	if shopSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug is required"})
		return
	}

	if branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Branch slug is required"})
		return
	}

	metrics, err := h.orderService.GetRealTimeMetricsBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.Error("Failed to get real-time metrics by slug: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get real-time metrics"})
		return
	}

	c.JSON(http.StatusOK, metrics)
}
