/**
 * NextAuth-aware fetch client for communicating with the backend API
 * Handles NextAuth JWT token exchange and authentication
 */

import { getToken } from 'next-auth/jwt'
import { getSession } from 'next-auth/react'
import { BackendApiError, type FetchOptions } from './fetchClient'
import { getBackendAuthHeaders } from '@/lib/auth/backendAuth'

// Backend API configuration
const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8080/api/v1';

console.log('BACKEND_API_URL:', BACKEND_API_URL);

/**
 * Server-side fetch client that uses NextAuth JWT tokens
 * Use this in API routes and server components
 */
export async function serverFetchClient(
  endpoint: string,
  request: Request,
  options: FetchOptions = {}
): Promise<Response> {
  const {
    timeout = 30000,
    headers = {},
    ...restOptions
  } = options;

  // Ensure endpoint starts with /
  const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  const url = `${BACKEND_API_URL}${normalizedEndpoint}`;

  console.log('serverFetchClient - Attempting to fetch:', url);

  // Get authentication headers
  const authHeaders = await getBackendAuthHeaders(request);

  // Default headers
  const defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...authHeaders,
    ...headers,
  };

  // Create abort controller for timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...restOptions,
      headers: defaultHeaders,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    // Handle non-2xx responses
    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      let errorCode: string | undefined;

      try {
        const errorData = await response.json();
        if (errorData.message) {
          errorMessage = errorData.message;
        }
        if (errorData.code) {
          errorCode = errorData.code;
        }
      } catch {
        // If we can't parse the error response, use the default message
      }

      throw new BackendApiError(errorMessage, response.status, errorCode);
    }

    return response;
  } catch (error) {
    clearTimeout(timeoutId);

    if (error instanceof BackendApiError) {
      throw error;
    }

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new BackendApiError('Request timeout', 408, 'TIMEOUT');
      }

      throw new BackendApiError(
        `Network error: ${error.message}`,
        0,
        'NETWORK_ERROR'
      );
    }

    throw new BackendApiError('Unknown error occurred', 500, 'UNKNOWN_ERROR');
  }
}

/**
 * Client-side fetch client that uses NextAuth session
 * Use this in client components and hooks
 */
export async function clientFetchClient(
  endpoint: string,
  options: FetchOptions = {}
): Promise<Response> {
  const {
    timeout = 30000,
    headers = {},
    ...restOptions
  } = options;

  // Get NextAuth session on client side
  const session = await getSession();

  // Ensure endpoint starts with /
  const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  const url = `${BACKEND_API_URL}${normalizedEndpoint}`;

  // Default headers
  const defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...headers,
  };

  // Add authentication header if session is available
  if (session?.user) {
    // Add user info as headers for backend authentication
    defaultHeaders['X-User-ID'] = session.user.id;
    defaultHeaders['X-User-Email'] = session.user.email;
    defaultHeaders['X-User-Role'] = session.user.role;

    // You might also want to send a session token
    // defaultHeaders['Authorization'] = `Bearer ${session.accessToken}`;
  }

  // Create abort controller for timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...restOptions,
      headers: defaultHeaders,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      let errorCode: string | undefined;

      try {
        const errorData = await response.json();
        if (errorData.message) {
          errorMessage = errorData.message;
        }
        if (errorData.code) {
          errorCode = errorData.code;
        }
      } catch {
        // Use default error message
      }

      throw new BackendApiError(errorMessage, response.status, errorCode);
    }

    return response;
  } catch (error) {
    clearTimeout(timeoutId);

    if (error instanceof BackendApiError) {
      throw error;
    }

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new BackendApiError('Request timeout', 408, 'TIMEOUT');
      }

      throw new BackendApiError(
        `Network error: ${error.message}`,
        0,
        'NETWORK_ERROR'
      );
    }

    throw new BackendApiError('Unknown error occurred', 500, 'UNKNOWN_ERROR');
  }
}

/**
 * Convenience methods for server-side requests
 */
export async function serverFetchGet(endpoint: string, request: Request, options: FetchOptions = {}) {
  return serverFetchClient(endpoint, request, { ...options, method: 'GET' });
}

export async function serverFetchPost(endpoint: string, request: Request, data?: any, options: FetchOptions = {}) {
  return serverFetchClient(endpoint, request, {
    ...options,
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * Convenience methods for client-side requests
 */
export async function clientFetchGet(endpoint: string, options: FetchOptions = {}) {
  return clientFetchClient(endpoint, { ...options, method: 'GET' });
}

export async function clientFetchPost(endpoint: string, data?: any, options: FetchOptions = {}) {
  return clientFetchClient(endpoint, {
    ...options,
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  });
}
