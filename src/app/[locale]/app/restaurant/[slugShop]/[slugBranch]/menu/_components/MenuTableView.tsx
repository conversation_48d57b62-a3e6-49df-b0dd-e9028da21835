'use client';

import React from 'react';
import { Link } from '@/i18n/navigation';
import { MenuEmptyState } from './MenuEmptyState';

interface MenuItem {
  id: string;
  name: string;
  slug: string;
  description?: string;
  category: string;
  price: number;
  image?: string;
  available: boolean;
}

interface MenuTableViewProps {
  items: MenuItem[];
  slugShop: string;
  slugBranch: string;
  searchTerm: string;
}

export function MenuTableView({ items, slugShop, slugBranch, searchTerm }: MenuTableViewProps) {
  if (items.length === 0) {
    return (
      <MenuEmptyState
        slugShop={slugShop}
        slugBranch={slugBranch}
        searchTerm={searchTerm}
      />
    );
  }

  return (
    <div className="px-4 py-3">
      <div className="flex overflow-hidden rounded-xl border border-[#e3e1dd] bg-white">
        <table className="flex-1">
          <thead>
            <tr className="bg-white">
              <th className="px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">
                Item Name
              </th>
              <th className="px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">
                Category
              </th>
              <th className="px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">Price</th>
              <th className="px-4 py-3 text-left text-[#161412] w-60 text-sm font-medium leading-normal">Availability</th>
              <th className="px-4 py-3 text-left text-[#81766a] w-60 text-sm font-medium leading-normal">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {items.map((item) => (
              <tr key={item.id} className="border-t border-t-[#e3e1dd]">
                <td className="h-[72px] px-4 py-2 w-[400px] text-[#161412] text-sm font-normal leading-normal">
                  {item.name}
                </td>
                <td className="h-[72px] px-4 py-2 w-[400px] text-[#81766a] text-sm font-normal leading-normal">
                  {item.category}
                </td>
                <td className="h-[72px] px-4 py-2 w-[400px] text-[#81766a] text-sm font-normal leading-normal">
                  ${item.price.toFixed(2)}
                </td>
                <td className="h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                  <button
                    className={`flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 text-sm font-medium leading-normal w-full ${
                      item.available
                        ? 'bg-[#f4f2f1] text-[#161412]'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    <span className="truncate">{item.available ? 'Available' : 'Unavailable'}</span>
                  </button>
                </td>
                <td className="h-[72px] px-4 py-2 w-60 text-[#81766a] text-sm font-bold leading-normal tracking-[0.015em]">
                  <div className="flex gap-2">
                    <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/${item.slug}`} className="hover:text-[#161412] transition-colors">
                      View
                    </Link>
                    <span className="text-[#81766a]">|</span>
                    <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/${item.slug}/edit`} className="hover:text-[#161412] transition-colors">
                      Edit
                    </Link>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
