'use client';

import React from 'react';
import { MenuItemCard } from './MenuItemCard';
import { MenuEmptyState } from './MenuEmptyState';

interface MenuItem {
  id: string;
  name: string;
  slug: string;
  description?: string;
  category: string;
  price: number;
  image?: string;
  available: boolean;
}

interface MenuGridViewProps {
  items: MenuItem[];
  slugShop: string;
  slugBranch: string;
  searchTerm: string;
}

export function MenuGridView({ items, slugShop, slugBranch, searchTerm }: MenuGridViewProps) {
  if (items.length === 0) {
    return (
      <MenuEmptyState
        slugShop={slugShop}
        slugBranch={slugBranch}
        searchTerm={searchTerm}
      />
    );
  }

  return (
    <div className="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
      {items.map((item) => (
        <MenuItemCard
          key={item.id}
          item={item}
          slugShop={slugShop}
          slugBranch={slugBranch}
        />
      ))}
    </div>
  );
}
